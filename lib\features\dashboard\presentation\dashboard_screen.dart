import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../shared/widgets/admin_layout.dart';
import '../../../core/routing/admin_router.dart';
import '../../../core/theme/admin_theme.dart';
import '../providers/dashboard_provider.dart';
import '../models/dashboard_stats_model.dart';

/// شاشة لوحة المراقبة الرئيسية
class DashboardScreen extends ConsumerWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AdminLayout(
      currentRoute: AdminRoutes.dashboard,
      child: _buildDashboardContent(ref),
    );
  }

  Widget _buildDashboardContent(WidgetRef ref) {
    final dashboardStatsAsync = ref.watch(dashboardStatsProvider);
    final recentActivityAsync = ref.watch(recentActivityProvider);

    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(dashboardStatsProvider);
        ref.invalidate(recentActivityProvider);
      },
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقات الإحصائيات الرئيسية
            dashboardStatsAsync.when(
              data: (stats) => _buildStatsCards(stats),
              loading: () => _buildStatsCardsLoading(),
              error: (error, stack) => _buildStatsCardsError(),
            ),
            const SizedBox(height: 24),

            // رسالة ترحيب
            _buildWelcomeMessage(),
            const SizedBox(height: 24),

            // إحصائيات سريعة إضافية
            dashboardStatsAsync.when(
              data: (stats) => _buildQuickStats(stats),
              loading: () => _buildQuickStatsLoading(),
              error: (error, stack) => const SizedBox.shrink(),
            ),
            const SizedBox(height: 24),

            // نشاط حديث
            recentActivityAsync.when(
              data: (activities) => _buildRecentActivity(activities),
              loading: () => _buildRecentActivityLoading(),
              error: (error, stack) => _buildRecentActivityError(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCards(DashboardStats stats) {
    return Builder(
      builder: (context) => GridView.count(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: _getGridCrossAxisCount(context),
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 2.5,
        children: [
          _buildStatsCard(
            title: 'إجمالي المستخدمين',
            value: _formatNumber(stats.totalUsers),
            icon: Icons.people,
            color: AdminColors.primary,
            subtitle: '${stats.activeUsers} نشط',
          ),
          _buildStatsCard(
            title: 'إجمالي الأجهزة',
            value: _formatNumber(stats.totalDevices),
            icon: Icons.devices,
            color: AdminColors.secondary,
            subtitle: '${stats.trustedDevices} موثوق',
          ),
          _buildStatsCard(
            title: 'إجمالي الصور',
            value: _formatNumber(stats.totalPhotos),
            icon: Icons.photo_library,
            color: AdminColors.accent,
            subtitle: '${stats.photosToday} اليوم',
          ),
          _buildStatsCard(
            title: 'إجمالي الفيديوهات',
            value: _formatNumber(stats.totalVideos),
            icon: Icons.video_library,
            color: AdminColors.info,
            subtitle: '${stats.videosToday} اليوم',
          ),
          _buildStatsCard(
            title: 'مساحة التخزين',
            value: stats.usedStorageFormatted,
            icon: Icons.storage,
            color: AdminColors.warning,
            subtitle: 'مستخدمة',
          ),
          _buildStatsCard(
            title: 'مواقع U',
            value: _formatNumber(stats.uLocations),
            icon: Icons.location_on,
            color: AdminColors.success,
            subtitle: 'من ${stats.totalLocations} موقع',
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCardsLoading() {
    return Builder(
      builder: (context) => GridView.count(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: _getGridCrossAxisCount(context),
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 2.5,
        children: List.generate(6, (index) => _buildStatsCardSkeleton()),
      ),
    );
  }

  Widget _buildStatsCardsError() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AdminColors.card,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AdminColors.borderLight),
      ),
      child: const Center(
        child: Column(
          children: [
            Icon(Icons.error_outline, size: 48, color: AdminColors.error),
            SizedBox(height: 16),
            Text(
              'فشل في تحميل الإحصائيات',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AdminColors.textPrimary,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'يرجى المحاولة مرة أخرى',
              style: TextStyle(color: AdminColors.textSecondary),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required String subtitle,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AdminColors.card,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AdminColors.borderLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              const Spacer(),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: AdminColors.textPrimary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AdminColors.textPrimary,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: const TextStyle(
              fontSize: 12,
              color: AdminColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeMessage() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AdminColors.card,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AdminColors.borderLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AdminColors.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.brightness_2,
              color: AdminColors.primary,
              size: 48,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'مرحباً بك في لوحة إدارة ذاكرة القمر',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AdminColors.textPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          const Text(
            'نظرة شاملة على نشاط النظام والإحصائيات في الوقت الفعلي',
            style: TextStyle(
              fontSize: 16,
              color: AdminColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton.icon(
                onPressed: () {},
                icon: const Icon(Icons.refresh),
                label: const Text('تحديث البيانات'),
              ),
              const SizedBox(width: 16),
              OutlinedButton.icon(
                onPressed: () {},
                icon: const Icon(Icons.settings),
                label: const Text('الإعدادات'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    return Row(
      children: [
        Expanded(
          child: _buildQuickStatCard(
            title: 'نشاط اليوم',
            value: '342',
            icon: Icons.today,
            color: AdminColors.success,
            trend: '+12%',
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildQuickStatCard(
            title: 'نشاط الأسبوع',
            value: '2,156',
            icon: Icons.date_range,
            color: AdminColors.info,
            trend: '+8%',
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildQuickStatCard(
            title: 'نشاط الشهر',
            value: '8,943',
            icon: Icons.calendar_month,
            color: AdminColors.warning,
            trend: '+15%',
          ),
        ),
      ],
    );
  }

  Widget _buildQuickStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required String trend,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AdminColors.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AdminColors.borderLight),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AdminColors.textPrimary,
                  ),
                ),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AdminColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Text(
            trend,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AdminColors.card,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AdminColors.borderLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'النشاط الحديث',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AdminColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          _buildActivityItem(
            icon: Icons.person_add,
            title: 'مستخدم جديد انضم',
            subtitle: 'أحمد محمد - منذ 5 دقائق',
            color: AdminColors.success,
          ),
          _buildActivityItem(
            icon: Icons.photo_library,
            title: 'تم رفع 15 صورة جديدة',
            subtitle: 'الموقع U105 - منذ 12 دقيقة',
            color: AdminColors.info,
          ),
          _buildActivityItem(
            icon: Icons.security,
            title: 'تم التحقق من جهاز جديد',
            subtitle: 'iPhone 15 Pro - منذ 25 دقيقة',
            color: AdminColors.warning,
          ),
          _buildActivityItem(
            icon: Icons.backup,
            title: 'تمت النسخة الاحتياطية بنجاح',
            subtitle: 'حجم 2.4 GB - منذ ساعة',
            color: AdminColors.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 16),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AdminColors.textPrimary,
                  ),
                ),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AdminColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  int _getGridCrossAxisCount(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    if (width > 1200) return 3;
    if (width > 800) return 2;
    return 1;
  }

  /// تنسيق الأرقام لعرض أفضل
  String _formatNumber(int number) {
    if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    }
    return number.toString();
  }

  /// بناء skeleton للبطاقات أثناء التحميل
  Widget _buildStatsCardSkeleton() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AdminColors.card,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AdminColors.borderLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AdminColors.borderLight,
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: double.infinity,
                      height: 16,
                      decoration: BoxDecoration(
                        color: AdminColors.borderLight,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: 80,
                      height: 12,
                      decoration: BoxDecoration(
                        color: AdminColors.borderLight,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء إحصائيات سريعة مع البيانات
  Widget _buildQuickStats(DashboardStats stats) {
    return Row(
      children: [
        Expanded(
          child: _buildQuickStatCard(
            title: 'معدل النشاط',
            value: stats.userActivityRateFormatted,
            color: AdminColors.success,
            icon: Icons.trending_up,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildQuickStatCard(
            title: 'استخدام المواقع',
            value: stats.locationUsageRateFormatted,
            color: AdminColors.info,
            icon: Icons.location_on,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildQuickStatCard(
            title: 'الأجهزة الموثوقة',
            value: stats.deviceTrustRateFormatted,
            color: AdminColors.warning,
            icon: Icons.security,
          ),
        ),
      ],
    );
  }

  /// بناء skeleton للإحصائيات السريعة
  Widget _buildQuickStatsLoading() {
    return Row(
      children: [
        Expanded(child: _buildQuickStatCardSkeleton()),
        const SizedBox(width: 16),
        Expanded(child: _buildQuickStatCardSkeleton()),
        const SizedBox(width: 16),
        Expanded(child: _buildQuickStatCardSkeleton()),
      ],
    );
  }

  Widget _buildQuickStatCardSkeleton() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AdminColors.card,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AdminColors.borderLight),
      ),
      child: Column(
        children: [
          Container(
            width: double.infinity,
            height: 16,
            decoration: BoxDecoration(
              color: AdminColors.borderLight,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            width: 60,
            height: 20,
            decoration: BoxDecoration(
              color: AdminColors.borderLight,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء النشاط الحديث مع البيانات
  Widget _buildRecentActivity(List<Map<String, dynamic>> activities) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AdminColors.card,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AdminColors.borderLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'النشاط الحديث',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AdminColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          ...activities.map((activity) => _buildActivityItem(activity)),
        ],
      ),
    );
  }

  Widget _buildActivityItem(Map<String, dynamic> activity) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getActivityColor(activity['color'] as String? ?? 'primary'),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getActivityIcon(activity['icon'] as String? ?? 'info'),
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity['title'],
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    color: AdminColors.textPrimary,
                  ),
                ),
                Text(
                  activity['subtitle'],
                  style: const TextStyle(
                    fontSize: 12,
                    color: AdminColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getActivityColor(String colorName) {
    switch (colorName) {
      case 'success': return AdminColors.success;
      case 'info': return AdminColors.info;
      case 'warning': return AdminColors.warning;
      case 'error': return AdminColors.error;
      default: return AdminColors.primary;
    }
  }

  IconData _getActivityIcon(String iconName) {
    switch (iconName) {
      case 'person_add': return Icons.person_add;
      case 'photo_library': return Icons.photo_library;
      case 'security': return Icons.security;
      default: return Icons.info;
    }
  }

  /// بناء skeleton للنشاط الحديث
  Widget _buildRecentActivityLoading() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AdminColors.card,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AdminColors.borderLight),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'النشاط الحديث',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AdminColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          ...List.generate(3, (index) => _buildActivityItemSkeleton()),
        ],
      ),
    );
  }

  Widget _buildActivityItemSkeleton() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AdminColors.borderLight,
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: double.infinity,
                  height: 16,
                  decoration: BoxDecoration(
                    color: AdminColors.borderLight,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  width: 120,
                  height: 12,
                  decoration: BoxDecoration(
                    color: AdminColors.borderLight,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء خطأ النشاط الحديث
  Widget _buildRecentActivityError() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AdminColors.card,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AdminColors.borderLight),
      ),
      child: const Center(
        child: Column(
          children: [
            Icon(Icons.error_outline, size: 48, color: AdminColors.error),
            SizedBox(height: 16),
            Text(
              'فشل في تحميل النشاط الحديث',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AdminColors.textPrimary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
