/// نموذج بيانات المستخدم - متوافق مع قاعدة البيانات الجديدة
class UserModel {
  final String id;
  final String fullName;
  final String nationalId;
  final String email;
  final String username;
  final String passwordHash;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastLoginAt;
  final bool isActive;
  final bool isVerified;
  final int totalPhotos;
  final int totalVideos;
  final double totalStorageMb;
  final String? notes;
  final List<String> permissions;

  const UserModel({
    required this.id,
    required this.fullName,
    required this.nationalId,
    required this.email,
    required this.username,
    required this.passwordHash,
    required this.createdAt,
    required this.updatedAt,
    this.lastLoginAt,
    required this.isActive,
    required this.isVerified,
    required this.totalPhotos,
    required this.totalVideos,
    required this.totalStorageMb,
    this.notes,
    this.permissions = const [],
  });

  UserModel copyWith({
    String? id,
    String? fullName,
    String? nationalId,
    String? email,
    String? username,
    String? passwordHash,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
    bool? isActive,
    bool? isVerified,
    int? totalPhotos,
    int? totalVideos,
    double? totalStorageMb,
    String? notes,
    List<String>? permissions,
  }) {
    return UserModel(
      id: id ?? this.id,
      fullName: fullName ?? this.fullName,
      nationalId: nationalId ?? this.nationalId,
      email: email ?? this.email,
      username: username ?? this.username,
      passwordHash: passwordHash ?? this.passwordHash,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      isActive: isActive ?? this.isActive,
      isVerified: isVerified ?? this.isVerified,
      totalPhotos: totalPhotos ?? this.totalPhotos,
      totalVideos: totalVideos ?? this.totalVideos,
      totalStorageMb: totalStorageMb ?? this.totalStorageMb,
      notes: notes ?? this.notes,
      permissions: permissions ?? this.permissions,
    );
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      fullName: json['full_name'] as String,
      nationalId: json['national_id'] as String,
      email: json['email'] as String,
      username: json['username'] as String,
      passwordHash: json['password_hash'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      lastLoginAt: json['last_login_at'] != null
        ? DateTime.parse(json['last_login_at'] as String)
        : null,
      isActive: json['is_active'] as bool,
      isVerified: json['is_verified'] as bool,
      totalPhotos: json['total_photos'] as int,
      totalVideos: json['total_videos'] as int,
      totalStorageMb: (json['total_storage_mb'] as num).toDouble(),
      notes: json['notes'] as String?,
      permissions: List<String>.from(json['permissions'] as List? ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'full_name': fullName,
      'national_id': nationalId,
      'email': email,
      'username': username,
      'password_hash': passwordHash,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'last_login_at': lastLoginAt?.toIso8601String(),
      'is_active': isActive,
      'is_verified': isVerified,
      'total_photos': totalPhotos,
      'total_videos': totalVideos,
      'total_storage_mb': totalStorageMb,
      'notes': notes,
      'permissions': permissions,
    };
  }
}

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserModel(id: $id, fullName: $fullName, email: $email)';
  }

  /// الحصول على إجمالي الوسائط
  int get totalMedia => totalPhotos + totalVideos;

  /// الحصول على حجم التخزين بصيغة قابلة للقراءة
  String get storageFormatted {
    if (totalStorageMb < 1024) return '${totalStorageMb.toStringAsFixed(1)} MB';
    final gb = totalStorageMb / 1024;
    if (gb < 1024) return '${gb.toStringAsFixed(1)} GB';
    final tb = gb / 1024;
    return '${tb.toStringAsFixed(1)} TB';
  }

  /// التحقق من كون المستخدم نشط حديثاً (خلال آخر 7 أيام)
  bool get isRecentlyActive {
    if (lastLoginAt == null) return false;
    final now = DateTime.now();
    final difference = now.difference(lastLoginAt!);
    return difference.inDays <= 7;
  }

  /// التحقق من كون المستخدم جديد (أقل من 30 يوم)
  bool get isNewUser {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    return difference.inDays <= 30;
  }

/// أدوار المستخدمين
enum UserRole {
  admin,
  user,
  moderator,
  viewer,
}

/// امتداد لأدوار المستخدمين
extension UserRoleExtension on UserRole {
  String get displayName {
    switch (this) {
      case UserRole.admin:
        return 'مشرف';
      case UserRole.user:
        return 'مستخدم';
      case UserRole.moderator:
        return 'مراقب';
      case UserRole.viewer:
        return 'مشاهد';
    }
  }

  String get description {
    switch (this) {
      case UserRole.admin:
        return 'صلاحيات كاملة للنظام';
      case UserRole.user:
        return 'مستخدم عادي';
      case UserRole.moderator:
        return 'مراقب المحتوى';
      case UserRole.viewer:
        return 'مشاهدة فقط';
    }
  }
}

/// حالة المستخدم
enum UserStatus {
  active,      // نشط
  inactive,    // غير نشط
  suspended,   // معلق
  pending,     // في الانتظار
  blocked,     // محظور
  deleted,     // محذوف
}

extension UserStatusExtension on UserStatus {
  String get displayName {
    switch (this) {
      case UserStatus.active:
        return 'نشط';
      case UserStatus.inactive:
        return 'غير نشط';
      case UserStatus.suspended:
        return 'معلق';
      case UserStatus.pending:
        return 'في الانتظار';
      case UserStatus.blocked:
        return 'محظور';
      case UserStatus.deleted:
        return 'محذوف';
    }
  }
}

/// فلاتر البحث للمستخدمين
class UserFilters {
  final String? searchQuery;
  final UserRole? role;
  final UserStatus? status;
  final DateTime? createdAfter;
  final DateTime? createdBefore;
  final DateTime? lastLoginAfter;
  final DateTime? lastLoginBefore;
  final bool? hasDevices;
  final bool? hasMedia;
  final int pageSize;
  final int pageOffset;
  final String sortBy;
  final bool sortDescending;

  const UserFilters({
    this.searchQuery,
    this.role,
    this.status,
    this.createdAfter,
    this.createdBefore,
    this.lastLoginAfter,
    this.lastLoginBefore,
    this.hasDevices,
    this.hasMedia,
    this.pageSize = 20,
    this.pageOffset = 0,
    this.sortBy = 'createdAt',
    this.sortDescending = false,
  });

  UserFilters copyWith({
    String? searchQuery,
    UserRole? role,
    UserStatus? status,
    DateTime? createdAfter,
    DateTime? createdBefore,
    DateTime? lastLoginAfter,
    DateTime? lastLoginBefore,
    bool? hasDevices,
    bool? hasMedia,
    int? pageSize,
    int? pageOffset,
    String? sortBy,
    bool? sortDescending,
  }) {
    return UserFilters(
      searchQuery: searchQuery ?? this.searchQuery,
      role: role ?? this.role,
      status: status ?? this.status,
      createdAfter: createdAfter ?? this.createdAfter,
      createdBefore: createdBefore ?? this.createdBefore,
      lastLoginAfter: lastLoginAfter ?? this.lastLoginAfter,
      lastLoginBefore: lastLoginBefore ?? this.lastLoginBefore,
      hasDevices: hasDevices ?? this.hasDevices,
      hasMedia: hasMedia ?? this.hasMedia,
      pageSize: pageSize ?? this.pageSize,
      pageOffset: pageOffset ?? this.pageOffset,
      sortBy: sortBy ?? this.sortBy,
      sortDescending: sortDescending ?? this.sortDescending,
    );
  }
}

/// إحصائيات المستخدمين
class UserStats {
  final int totalUsers;
  final int activeUsers;
  final int inactiveUsers;
  final int suspendedUsers;
  final int pendingUsers;
  final int adminUsers;
  final int regularUsers;
  final int usersWithDevices;
  final int usersWithMedia;
  final double averageStorageUsage;
  final int newUsersToday;
  final int newUsersThisWeek;
  final int newUsersThisMonth;

  const UserStats({
    required this.totalUsers,
    required this.activeUsers,
    required this.inactiveUsers,
    required this.suspendedUsers,
    required this.pendingUsers,
    required this.adminUsers,
    required this.regularUsers,
    required this.usersWithDevices,
    required this.usersWithMedia,
    required this.averageStorageUsage,
    required this.newUsersToday,
    required this.newUsersThisWeek,
    required this.newUsersThisMonth,
  });
}
