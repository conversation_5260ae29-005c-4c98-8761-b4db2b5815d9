/// نموذج بيانات الموقع - متوافق مع نظام المواقع الجديد (70 موقع)
class LocationModel {
  final String id;
  final String locationCode;
  final String locationType;
  final String locationNumber;
  final String locationNameAr;
  final String? locationNameEn;
  final String? descriptionAr;
  final String? descriptionEn;
  final int sortOrder;
  final String? category;
  final String? department;
  final bool isActive;
  final bool isAvailable;
  final int totalPhotos;
  final int totalVideos;
  final DateTime? lastUsedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  const LocationModel({
    required this.id,
    required this.locationCode,
    required this.locationType,
    required this.locationNumber,
    required this.locationNameAr,
    this.locationNameEn,
    this.descriptionAr,
    this.descriptionEn,
    required this.sortOrder,
    this.category,
    this.department,
    required this.isActive,
    required this.isAvailable,
    required this.totalPhotos,
    required this.totalVideos,
    this.lastUsedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  LocationModel copyWith({
    String? id,
    String? locationCode,
    String? locationType,
    String? locationNumber,
    String? locationNameAr,
    String? locationNameEn,
    String? descriptionAr,
    String? descriptionEn,
    int? sortOrder,
    String? category,
    String? department,
    bool? isActive,
    bool? isAvailable,
    int? totalPhotos,
    int? totalVideos,
    DateTime? lastUsedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LocationModel(
      id: id ?? this.id,
      locationCode: locationCode ?? this.locationCode,
      locationType: locationType ?? this.locationType,
      locationNumber: locationNumber ?? this.locationNumber,
      locationNameAr: locationNameAr ?? this.locationNameAr,
      locationNameEn: locationNameEn ?? this.locationNameEn,
      descriptionAr: descriptionAr ?? this.descriptionAr,
      descriptionEn: descriptionEn ?? this.descriptionEn,
      sortOrder: sortOrder ?? this.sortOrder,
      category: category ?? this.category,
      department: department ?? this.department,
      isActive: isActive ?? this.isActive,
      isAvailable: isAvailable ?? this.isAvailable,
      totalPhotos: totalPhotos ?? this.totalPhotos,
      totalVideos: totalVideos ?? this.totalVideos,
      lastUsedAt: lastUsedAt ?? this.lastUsedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  factory LocationModel.fromJson(Map<String, dynamic> json) {
    return LocationModel(
      id: json['id'] as String,
      locationCode: json['location_code'] as String,
      locationType: json['location_type'] as String,
      locationNumber: json['location_number'] as String,
      locationNameAr: json['location_name_ar'] as String,
      locationNameEn: json['location_name_en'] as String?,
      descriptionAr: json['description_ar'] as String?,
      descriptionEn: json['description_en'] as String?,
      sortOrder: json['sort_order'] as int,
      category: json['category'] as String?,
      department: json['department'] as String?,
      isActive: json['is_active'] as bool,
      isAvailable: json['is_available'] as bool,
      totalPhotos: json['total_photos'] as int,
      totalVideos: json['total_videos'] as int,
      lastUsedAt: json['last_used_at'] != null
          ? DateTime.parse(json['last_used_at'] as String)
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'location_code': locationCode,
      'location_type': locationType,
      'location_number': locationNumber,
      'location_name_ar': locationNameAr,
      'location_name_en': locationNameEn,
      'description_ar': descriptionAr,
      'description_en': descriptionEn,
      'sort_order': sortOrder,
      'category': category,
      'department': department,
      'is_active': isActive,
      'is_available': isAvailable,
      'total_photos': totalPhotos,
      'total_videos': totalVideos,
      'last_used_at': lastUsedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'LocationModel(id: $id, locationCode: $locationCode, locationNameAr: $locationNameAr)';
  }

  /// الحصول على الاسم المناسب حسب اللغة
  String getDisplayName([String locale = 'ar']) {
    if (locale == 'en' && locationNameEn != null && locationNameEn!.isNotEmpty) {
      return locationNameEn!;
    }
    return locationNameAr;
  }

  /// الحصول على الوصف المناسب حسب اللغة
  String? getDescription([String locale = 'ar']) {
    if (locale == 'en' && descriptionEn != null && descriptionEn!.isNotEmpty) {
      return descriptionEn;
    }
    return descriptionAr;
  }

  /// التحقق من كون الموقع من نوع U
  bool get isULocation => locationType == 'U';

  /// التحقق من كون الموقع من نوع C
  bool get isCLocation => locationType == 'C';

  /// الحصول على إجمالي الوسائط
  int get totalMedia => totalPhotos + totalVideos;

  /// التحقق من وجود نشاط حديث (خلال آخر 7 أيام)
  bool get hasRecentActivity {
    if (lastUsedAt == null) return false;
    final now = DateTime.now();
    final difference = now.difference(lastUsedAt!);
    return difference.inDays <= 7;
  }

  /// الحصول على حالة الموقع
  LocationStatus get status {
    if (!isActive) return LocationStatus.inactive;
    if (!isAvailable) return LocationStatus.unavailable;
    if (hasRecentActivity) return LocationStatus.active;
    return LocationStatus.idle;
  }
}

/// حالات الموقع
enum LocationStatus {
  active,      // نشط
  idle,        // خامل
  inactive,    // غير نشط
  unavailable, // غير متاح
}

/// أنواع المواقع
enum LocationType {
  U, // مواقع U
  C, // مواقع C
}

/// فلاتر البحث للمواقع
class LocationFilters {
  final String? searchQuery;
  final LocationType? locationType;
  final LocationStatus? status;
  final String? category;
  final String? department;
  final bool? isActive;
  final bool? isAvailable;
  final bool? hasRecentActivity;
  final DateTime? lastUsedAfter;
  final DateTime? lastUsedBefore;
  final int? minPhotos;
  final int? maxPhotos;
  final int? minVideos;
  final int? maxVideos;
  final int pageSize;
  final int pageOffset;
  final String sortBy;
  final bool sortDescending;

  const LocationFilters({
    this.searchQuery,
    this.locationType,
    this.status,
    this.category,
    this.department,
    this.isActive,
    this.isAvailable,
    this.hasRecentActivity,
    this.lastUsedAfter,
    this.lastUsedBefore,
    this.minPhotos,
    this.maxPhotos,
    this.minVideos,
    this.maxVideos,
    this.pageSize = 20,
    this.pageOffset = 0,
    this.sortBy = 'sort_order',
    this.sortDescending = false,
  });

  LocationFilters copyWith({
    String? searchQuery,
    LocationType? locationType,
    LocationStatus? status,
    String? category,
    String? department,
    bool? isActive,
    bool? isAvailable,
    bool? hasRecentActivity,
    DateTime? lastUsedAfter,
    DateTime? lastUsedBefore,
    int? minPhotos,
    int? maxPhotos,
    int? minVideos,
    int? maxVideos,
    int? pageSize,
    int? pageOffset,
    String? sortBy,
    bool? sortDescending,
  }) {
    return LocationFilters(
      searchQuery: searchQuery ?? this.searchQuery,
      locationType: locationType ?? this.locationType,
      status: status ?? this.status,
      category: category ?? this.category,
      department: department ?? this.department,
      isActive: isActive ?? this.isActive,
      isAvailable: isAvailable ?? this.isAvailable,
      hasRecentActivity: hasRecentActivity ?? this.hasRecentActivity,
      lastUsedAfter: lastUsedAfter ?? this.lastUsedAfter,
      lastUsedBefore: lastUsedBefore ?? this.lastUsedBefore,
      minPhotos: minPhotos ?? this.minPhotos,
      maxPhotos: maxPhotos ?? this.maxPhotos,
      minVideos: minVideos ?? this.minVideos,
      maxVideos: maxVideos ?? this.maxVideos,
      pageSize: pageSize ?? this.pageSize,
      pageOffset: pageOffset ?? this.pageOffset,
      sortBy: sortBy ?? this.sortBy,
      sortDescending: sortDescending ?? this.sortDescending,
    );
  }
}
