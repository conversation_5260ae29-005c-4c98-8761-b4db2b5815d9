import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../config/supabase_config.dart';

/// مزود خدمة مصادقة المشرف
final adminAuthServiceProvider = Provider<AdminAuthService>((ref) {
  return AdminAuthService();
});

/// خدمة مصادقة المشرف المتقدمة
class AdminAuthService {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// التحقق من حالة المصادقة الحالية
  bool get isAuthenticated => _supabase.auth.currentUser != null;

  /// الحصول على المستخدم الحالي
  User? get currentUser => _supabase.auth.currentUser;

  /// الحصول على معرف المستخدم الحالي
  String? get currentUserId => _supabase.auth.currentUser?.id;

  /// تسجيل دخول المشرف
  Future<AuthResponse> signInAdmin({
    required String email,
    required String password,
  }) async {
    try {
      // محاولة تسجيل الدخول
      final response = await _supabase.auth.signInWithPassword(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // التحقق من صلاحيات المشرف
        final isAdmin = await _verifyAdminPermissions(response.user!.id);
        if (!isAdmin) {
          await signOut();
          throw Exception('ليس لديك صلاحيات المشرف');
        }

        // تسجيل محاولة تسجيل الدخول الناجحة
        await _logAuthAttempt(
          userId: response.user!.id,
          email: email,
          success: true,
          ipAddress: await _getClientIP(),
        );
      }

      return response;
    } catch (e) {
      // تسجيل محاولة تسجيل الدخول الفاشلة
      await _logAuthAttempt(
        email: email,
        success: false,
        error: e.toString(),
        ipAddress: await _getClientIP(),
      );
      rethrow;
    }
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    try {
      await _supabase.auth.signOut();
    } catch (e) {
      throw Exception('فشل في تسجيل الخروج: $e');
    }
  }

  /// التحقق من صلاحيات المشرف
  Future<bool> _verifyAdminPermissions(String userId) async {
    try {
      final response = await _supabase
          .from('users')
          .select('id, email, is_active')
          .eq('id', userId)
          .single();

      // التحقق من أن المستخدم نشط
      if (response['is_active'] != true) {
        return false;
      }

      // يمكن إضافة المزيد من التحققات هنا
      // مثل التحقق من جدول admin_users أو صلاحيات خاصة
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// تسجيل محاولات المصادقة
  Future<void> _logAuthAttempt({
    String? userId,
    required String email,
    required bool success,
    String? error,
    String? ipAddress,
  }) async {
    try {
      await _supabase.from('admin_auth_logs').insert({
        'user_id': userId,
        'email': email,
        'success': success,
        'error_message': error,
        'ip_address': ipAddress,
        'user_agent': 'Moon Memory Admin Web App',
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      // تجاهل أخطاء التسجيل لتجنب تعطيل المصادقة
      print('فشل في تسجيل محاولة المصادقة: $e');
    }
  }

  /// الحصول على عنوان IP للعميل
  Future<String?> _getClientIP() async {
    try {
      // في بيئة الويب، يمكن الحصول على IP من headers
      // هنا نعيد قيمة افتراضية
      return 'Unknown';
    } catch (e) {
      return null;
    }
  }

  /// إعادة تعيين كلمة المرور
  Future<void> resetPassword(String email) async {
    try {
      await _supabase.auth.resetPasswordForEmail(
        email,
        redirectTo: '${SupabaseConfig.supabaseUrl}/auth/reset-password',
      );
    } catch (e) {
      throw Exception('فشل في إرسال رابط إعادة تعيين كلمة المرور: $e');
    }
  }

  /// تحديث كلمة المرور
  Future<void> updatePassword(String newPassword) async {
    try {
      await _supabase.auth.updateUser(
        UserAttributes(password: newPassword),
      );
    } catch (e) {
      throw Exception('فشل في تحديث كلمة المرور: $e');
    }
  }

  /// الحصول على معلومات الجلسة الحالية
  Session? get currentSession => _supabase.auth.currentSession;

  /// التحقق من انتهاء صلاحية الجلسة
  bool get isSessionExpired {
    final session = currentSession;
    if (session == null) return true;
    
    final expiresAt = DateTime.fromMillisecondsSinceEpoch(session.expiresAt! * 1000);
    return DateTime.now().isAfter(expiresAt);
  }

  /// تجديد الجلسة
  Future<AuthResponse> refreshSession() async {
    try {
      final response = await _supabase.auth.refreshSession();
      return response;
    } catch (e) {
      throw Exception('فشل في تجديد الجلسة: $e');
    }
  }

  /// الاستماع لتغييرات حالة المصادقة
  Stream<AuthState> get authStateChanges => _supabase.auth.onAuthStateChange;

  /// التحقق من قوة كلمة المرور
  static bool isPasswordStrong(String password) {
    if (password.length < 8) return false;
    
    final hasUppercase = password.contains(RegExp(r'[A-Z]'));
    final hasLowercase = password.contains(RegExp(r'[a-z]'));
    final hasDigits = password.contains(RegExp(r'[0-9]'));
    final hasSpecialCharacters = password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
    
    return hasUppercase && hasLowercase && hasDigits && hasSpecialCharacters;
  }

  /// التحقق من صحة البريد الإلكتروني
  static bool isEmailValid(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// الحصول على إحصائيات المصادقة
  Future<Map<String, dynamic>> getAuthStats() async {
    try {
      // إحصائيات محاولات تسجيل الدخول
      final totalAttempts = await _supabase
          .from('admin_auth_logs')
          .select('id')
          .count();

      final successfulAttempts = await _supabase
          .from('admin_auth_logs')
          .select('id')
          .eq('success', true)
          .count();

      final failedAttempts = await _supabase
          .from('admin_auth_logs')
          .select('id')
          .eq('success', false)
          .count();

      // محاولات اليوم
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);
      
      final todayAttempts = await _supabase
          .from('admin_auth_logs')
          .select('id')
          .gte('created_at', startOfDay.toIso8601String())
          .count();

      return {
        'total_attempts': totalAttempts.count,
        'successful_attempts': successfulAttempts.count,
        'failed_attempts': failedAttempts.count,
        'today_attempts': todayAttempts.count,
        'success_rate': totalAttempts.count > 0 
            ? (successfulAttempts.count / totalAttempts.count * 100).toStringAsFixed(1)
            : '0.0',
      };
    } catch (e) {
      return {
        'total_attempts': 0,
        'successful_attempts': 0,
        'failed_attempts': 0,
        'today_attempts': 0,
        'success_rate': '0.0',
      };
    }
  }

  /// الحصول على سجل محاولات المصادقة الأخيرة
  Future<List<Map<String, dynamic>>> getRecentAuthLogs({int limit = 50}) async {
    try {
      final response = await _supabase
          .from('admin_auth_logs')
          .select('*')
          .order('created_at', ascending: false)
          .limit(limit);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      return [];
    }
  }
}
