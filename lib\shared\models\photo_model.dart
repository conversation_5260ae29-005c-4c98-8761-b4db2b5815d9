/// نموذج بيانات الصورة - متوافق مع قاعدة البيانات الجديدة
class PhotoModel {
  final String id;
  final String userId;
  final String fileName;
  final String? storagePath;
  final String? imageUrl;
  final int? fileSizeBytes;
  final String? locationType;
  final String? locationNumber;
  final String? fullLocationCode;
  final String username;
  final DateTime captureTimestamp;
  final DateTime uploadTimestamp;
  final int? sortOrder;
  final List<String> tags;
  final String? description;
  final Map<String, dynamic>? cameraSettings;
  final Map<String, double>? gpsCoordinates;
  final Map<String, dynamic>? weatherInfo;
  final PhotoStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;

  const PhotoModel({
    required this.id,
    required this.userId,
    required this.fileName,
    this.storagePath,
    this.imageUrl,
    this.fileSizeBytes,
    this.locationType,
    this.locationNumber,
    this.fullLocationCode,
    required this.username,
    required this.captureTimestamp,
    required this.uploadTimestamp,
    this.sortOrder,
    this.tags = const [],
    this.description,
    this.cameraSettings,
    this.gpsCoordinates,
    this.weatherInfo,
    this.status = PhotoStatus.active,
    required this.createdAt,
    required this.updatedAt,
  });

  PhotoModel copyWith({
    String? id,
    String? userId,
    String? fileName,
    String? storagePath,
    String? imageUrl,
    int? fileSizeBytes,
    String? locationType,
    String? locationNumber,
    String? fullLocationCode,
    String? username,
    DateTime? captureTimestamp,
    DateTime? uploadTimestamp,
    int? sortOrder,
    List<String>? tags,
    String? description,
    Map<String, dynamic>? cameraSettings,
    Map<String, double>? gpsCoordinates,
    Map<String, dynamic>? weatherInfo,
    PhotoStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PhotoModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      fileName: fileName ?? this.fileName,
      storagePath: storagePath ?? this.storagePath,
      imageUrl: imageUrl ?? this.imageUrl,
      fileSizeBytes: fileSizeBytes ?? this.fileSizeBytes,
      locationType: locationType ?? this.locationType,
      locationNumber: locationNumber ?? this.locationNumber,
      fullLocationCode: fullLocationCode ?? this.fullLocationCode,
      username: username ?? this.username,
      captureTimestamp: captureTimestamp ?? this.captureTimestamp,
      uploadTimestamp: uploadTimestamp ?? this.uploadTimestamp,
      sortOrder: sortOrder ?? this.sortOrder,
      tags: tags ?? this.tags,
      description: description ?? this.description,
      cameraSettings: cameraSettings ?? this.cameraSettings,
      gpsCoordinates: gpsCoordinates ?? this.gpsCoordinates,
      weatherInfo: weatherInfo ?? this.weatherInfo,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  factory PhotoModel.fromJson(Map<String, dynamic> json) {
    return PhotoModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      fileName: json['file_name'] as String,
      storagePath: json['storage_path'] as String?,
      imageUrl: json['image_url'] as String?,
      fileSizeBytes: json['file_size_bytes'] as int?,
      locationType: json['location_type'] as String?,
      locationNumber: json['location_number'] as String?,
      fullLocationCode: json['full_location_code'] as String?,
      username: json['username'] as String,
      captureTimestamp: DateTime.parse(json['capture_timestamp'] as String),
      uploadTimestamp: DateTime.parse(json['upload_timestamp'] as String),
      sortOrder: json['sort_order'] as int?,
      tags: List<String>.from(json['tags'] as List? ?? []),
      description: json['description'] as String?,
      cameraSettings: json['camera_settings'] as Map<String, dynamic>?,
      gpsCoordinates: json['gps_coordinates'] != null
          ? Map<String, double>.from(json['gps_coordinates'] as Map)
          : null,
      weatherInfo: json['weather_info'] as Map<String, dynamic>?,
      status: PhotoStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => PhotoStatus.active,
      ),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'file_name': fileName,
      'storage_path': storagePath,
      'image_url': imageUrl,
      'file_size_bytes': fileSizeBytes,
      'location_type': locationType,
      'location_number': locationNumber,
      'full_location_code': fullLocationCode,
      'username': username,
      'capture_timestamp': captureTimestamp.toIso8601String(),
      'upload_timestamp': uploadTimestamp.toIso8601String(),
      'sort_order': sortOrder,
      'tags': tags,
      'description': description,
      'camera_settings': cameraSettings,
      'gps_coordinates': gpsCoordinates,
      'weather_info': weatherInfo,
      'status': status.name,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PhotoModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PhotoModel(id: $id, fileName: $fileName, username: $username)';
  }

  /// الحصول على حجم الملف بصيغة قابلة للقراءة
  String get fileSizeFormatted {
    if (fileSizeBytes == null) return 'غير محدد';
    
    final bytes = fileSizeBytes!;
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// التحقق من وجود موقع GPS
  bool get hasGpsCoordinates => gpsCoordinates != null && gpsCoordinates!.isNotEmpty;

  /// الحصول على خط العرض
  double? get latitude => gpsCoordinates?['latitude'];

  /// الحصول على خط الطول
  double? get longitude => gpsCoordinates?['longitude'];

  /// التحقق من كون الصورة حديثة (خلال آخر 24 ساعة)
  bool get isRecent {
    final now = DateTime.now();
    final difference = now.difference(captureTimestamp);
    return difference.inHours <= 24;
  }

  /// الحصول على رابط الصورة المصغرة
  String? get thumbnailUrl {
    if (imageUrl == null) return null;
    // يمكن إضافة منطق لتوليد رابط الصورة المصغرة
    return imageUrl;
  }
}

/// حالات الصورة
enum PhotoStatus {
  active,   // نشطة
  deleted,  // محذوفة
  archived, // مؤرشفة
}

/// فلاتر البحث للصور
class PhotoFilters {
  final String? searchQuery;
  final String? userId;
  final String? username;
  final String? locationType;
  final String? locationNumber;
  final PhotoStatus? status;
  final DateTime? captureAfter;
  final DateTime? captureBefore;
  final DateTime? uploadAfter;
  final DateTime? uploadBefore;
  final List<String>? tags;
  final bool? hasGps;
  final int? minFileSize;
  final int? maxFileSize;
  final int pageSize;
  final int pageOffset;
  final String sortBy;
  final bool sortDescending;

  const PhotoFilters({
    this.searchQuery,
    this.userId,
    this.username,
    this.locationType,
    this.locationNumber,
    this.status,
    this.captureAfter,
    this.captureBefore,
    this.uploadAfter,
    this.uploadBefore,
    this.tags,
    this.hasGps,
    this.minFileSize,
    this.maxFileSize,
    this.pageSize = 20,
    this.pageOffset = 0,
    this.sortBy = 'capture_timestamp',
    this.sortDescending = true,
  });

  PhotoFilters copyWith({
    String? searchQuery,
    String? userId,
    String? username,
    String? locationType,
    String? locationNumber,
    PhotoStatus? status,
    DateTime? captureAfter,
    DateTime? captureBefore,
    DateTime? uploadAfter,
    DateTime? uploadBefore,
    List<String>? tags,
    bool? hasGps,
    int? minFileSize,
    int? maxFileSize,
    int? pageSize,
    int? pageOffset,
    String? sortBy,
    bool? sortDescending,
  }) {
    return PhotoFilters(
      searchQuery: searchQuery ?? this.searchQuery,
      userId: userId ?? this.userId,
      username: username ?? this.username,
      locationType: locationType ?? this.locationType,
      locationNumber: locationNumber ?? this.locationNumber,
      status: status ?? this.status,
      captureAfter: captureAfter ?? this.captureAfter,
      captureBefore: captureBefore ?? this.captureBefore,
      uploadAfter: uploadAfter ?? this.uploadAfter,
      uploadBefore: uploadBefore ?? this.uploadBefore,
      tags: tags ?? this.tags,
      hasGps: hasGps ?? this.hasGps,
      minFileSize: minFileSize ?? this.minFileSize,
      maxFileSize: maxFileSize ?? this.maxFileSize,
      pageSize: pageSize ?? this.pageSize,
      pageOffset: pageOffset ?? this.pageOffset,
      sortBy: sortBy ?? this.sortBy,
      sortDescending: sortDescending ?? this.sortDescending,
    );
  }
}
