/// نموذج إحصائيات لوحة المراقبة
class DashboardStats {
  // إحصائيات المستخدمين
  final int totalUsers;
  final int activeUsers;
  final int newUsersToday;
  final int newUsersThisWeek;
  
  // إحصائيات الوسائط
  final int totalPhotos;
  final int totalVideos;
  final int totalMedia;
  final int photosToday;
  final int videosToday;
  
  // إحصائيات الأجهزة
  final int totalDevices;
  final int trustedDevices;
  final int blockedDevices;
  
  // إحصائيات المواقع
  final int totalLocations;
  final int activeLocations;
  final int uLocations;
  final int cLocations;
  final int locationsWithMedia;
  
  // إحصائيات التخزين
  final double totalStorageGB;
  final double usedStorageGB;
  final double storageUsagePercentage;
  
  // معدلات الاستخدام
  final double userActivityRate;
  final double locationUsageRate;
  final double deviceTrustRate;
  
  // آخر تحديث
  final DateTime lastUpdated;

  const DashboardStats({
    required this.totalUsers,
    required this.activeUsers,
    required this.newUsersToday,
    required this.newUsersThisWeek,
    required this.totalPhotos,
    required this.totalVideos,
    required this.totalMedia,
    required this.photosToday,
    required this.videosToday,
    required this.totalDevices,
    required this.trustedDevices,
    required this.blockedDevices,
    required this.totalLocations,
    required this.activeLocations,
    required this.uLocations,
    required this.cLocations,
    required this.locationsWithMedia,
    required this.totalStorageGB,
    required this.usedStorageGB,
    required this.storageUsagePercentage,
    required this.userActivityRate,
    required this.locationUsageRate,
    required this.deviceTrustRate,
    required this.lastUpdated,
  });

  /// إنشاء إحصائيات فارغة
  factory DashboardStats.empty() {
    return DashboardStats(
      totalUsers: 0,
      activeUsers: 0,
      newUsersToday: 0,
      newUsersThisWeek: 0,
      totalPhotos: 0,
      totalVideos: 0,
      totalMedia: 0,
      photosToday: 0,
      videosToday: 0,
      totalDevices: 0,
      trustedDevices: 0,
      blockedDevices: 0,
      totalLocations: 0,
      activeLocations: 0,
      uLocations: 0,
      cLocations: 0,
      locationsWithMedia: 0,
      totalStorageGB: 0.0,
      usedStorageGB: 0.0,
      storageUsagePercentage: 0.0,
      userActivityRate: 0.0,
      locationUsageRate: 0.0,
      deviceTrustRate: 0.0,
      lastUpdated: DateTime.now(),
    );
  }

  DashboardStats copyWith({
    int? totalUsers,
    int? activeUsers,
    int? newUsersToday,
    int? newUsersThisWeek,
    int? totalPhotos,
    int? totalVideos,
    int? totalMedia,
    int? photosToday,
    int? videosToday,
    int? totalDevices,
    int? trustedDevices,
    int? blockedDevices,
    int? totalLocations,
    int? activeLocations,
    int? uLocations,
    int? cLocations,
    int? locationsWithMedia,
    double? totalStorageGB,
    double? usedStorageGB,
    double? storageUsagePercentage,
    double? userActivityRate,
    double? locationUsageRate,
    double? deviceTrustRate,
    DateTime? lastUpdated,
  }) {
    return DashboardStats(
      totalUsers: totalUsers ?? this.totalUsers,
      activeUsers: activeUsers ?? this.activeUsers,
      newUsersToday: newUsersToday ?? this.newUsersToday,
      newUsersThisWeek: newUsersThisWeek ?? this.newUsersThisWeek,
      totalPhotos: totalPhotos ?? this.totalPhotos,
      totalVideos: totalVideos ?? this.totalVideos,
      totalMedia: totalMedia ?? this.totalMedia,
      photosToday: photosToday ?? this.photosToday,
      videosToday: videosToday ?? this.videosToday,
      totalDevices: totalDevices ?? this.totalDevices,
      trustedDevices: trustedDevices ?? this.trustedDevices,
      blockedDevices: blockedDevices ?? this.blockedDevices,
      totalLocations: totalLocations ?? this.totalLocations,
      activeLocations: activeLocations ?? this.activeLocations,
      uLocations: uLocations ?? this.uLocations,
      cLocations: cLocations ?? this.cLocations,
      locationsWithMedia: locationsWithMedia ?? this.locationsWithMedia,
      totalStorageGB: totalStorageGB ?? this.totalStorageGB,
      usedStorageGB: usedStorageGB ?? this.usedStorageGB,
      storageUsagePercentage: storageUsagePercentage ?? this.storageUsagePercentage,
      userActivityRate: userActivityRate ?? this.userActivityRate,
      locationUsageRate: locationUsageRate ?? this.locationUsageRate,
      deviceTrustRate: deviceTrustRate ?? this.deviceTrustRate,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'total_users': totalUsers,
      'active_users': activeUsers,
      'new_users_today': newUsersToday,
      'new_users_this_week': newUsersThisWeek,
      'total_photos': totalPhotos,
      'total_videos': totalVideos,
      'total_media': totalMedia,
      'photos_today': photosToday,
      'videos_today': videosToday,
      'total_devices': totalDevices,
      'trusted_devices': trustedDevices,
      'blocked_devices': blockedDevices,
      'total_locations': totalLocations,
      'active_locations': activeLocations,
      'u_locations': uLocations,
      'c_locations': cLocations,
      'locations_with_media': locationsWithMedia,
      'total_storage_gb': totalStorageGB,
      'used_storage_gb': usedStorageGB,
      'storage_usage_percentage': storageUsagePercentage,
      'user_activity_rate': userActivityRate,
      'location_usage_rate': locationUsageRate,
      'device_trust_rate': deviceTrustRate,
      'last_updated': lastUpdated.toIso8601String(),
    };
  }

  /// إنشاء من JSON
  factory DashboardStats.fromJson(Map<String, dynamic> json) {
    return DashboardStats(
      totalUsers: json['total_users'] ?? 0,
      activeUsers: json['active_users'] ?? 0,
      newUsersToday: json['new_users_today'] ?? 0,
      newUsersThisWeek: json['new_users_this_week'] ?? 0,
      totalPhotos: json['total_photos'] ?? 0,
      totalVideos: json['total_videos'] ?? 0,
      totalMedia: json['total_media'] ?? 0,
      photosToday: json['photos_today'] ?? 0,
      videosToday: json['videos_today'] ?? 0,
      totalDevices: json['total_devices'] ?? 0,
      trustedDevices: json['trusted_devices'] ?? 0,
      blockedDevices: json['blocked_devices'] ?? 0,
      totalLocations: json['total_locations'] ?? 0,
      activeLocations: json['active_locations'] ?? 0,
      uLocations: json['u_locations'] ?? 0,
      cLocations: json['c_locations'] ?? 0,
      locationsWithMedia: json['locations_with_media'] ?? 0,
      totalStorageGB: (json['total_storage_gb'] ?? 0.0).toDouble(),
      usedStorageGB: (json['used_storage_gb'] ?? 0.0).toDouble(),
      storageUsagePercentage: (json['storage_usage_percentage'] ?? 0.0).toDouble(),
      userActivityRate: (json['user_activity_rate'] ?? 0.0).toDouble(),
      locationUsageRate: (json['location_usage_rate'] ?? 0.0).toDouble(),
      deviceTrustRate: (json['device_trust_rate'] ?? 0.0).toDouble(),
      lastUpdated: DateTime.parse(json['last_updated'] ?? DateTime.now().toIso8601String()),
    );
  }

  @override
  String toString() {
    return 'DashboardStats(totalUsers: $totalUsers, totalMedia: $totalMedia, totalLocations: $totalLocations)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DashboardStats && other.lastUpdated == lastUpdated;
  }

  @override
  int get hashCode => lastUpdated.hashCode;

  /// الحصول على نسبة المستخدمين النشطين
  String get userActivityRateFormatted => '${userActivityRate.toStringAsFixed(1)}%';

  /// الحصول على نسبة استخدام المواقع
  String get locationUsageRateFormatted => '${locationUsageRate.toStringAsFixed(1)}%';

  /// الحصول على نسبة الأجهزة الموثوقة
  String get deviceTrustRateFormatted => '${deviceTrustRate.toStringAsFixed(1)}%';

  /// الحصول على حجم التخزين المستخدم بصيغة قابلة للقراءة
  String get usedStorageFormatted {
    if (usedStorageGB < 1) {
      return '${(usedStorageGB * 1024).toStringAsFixed(0)} MB';
    } else if (usedStorageGB < 1024) {
      return '${usedStorageGB.toStringAsFixed(1)} GB';
    } else {
      return '${(usedStorageGB / 1024).toStringAsFixed(1)} TB';
    }
  }

  /// الحصول على نسبة استخدام التخزين
  String get storageUsageFormatted => '${storageUsagePercentage.toStringAsFixed(1)}%';

  /// التحقق من صحة النظام
  bool get isSystemHealthy {
    return userActivityRate > 50 && 
           deviceTrustRate > 70 && 
           storageUsagePercentage < 90;
  }

  /// الحصول على حالة النظام
  String get systemStatus {
    if (isSystemHealthy) return 'ممتاز';
    if (userActivityRate > 30 && deviceTrustRate > 50) return 'جيد';
    if (userActivityRate > 10) return 'متوسط';
    return 'يحتاج انتباه';
  }

  /// الحصول على لون حالة النظام
  String get systemStatusColor {
    switch (systemStatus) {
      case 'ممتاز': return 'success';
      case 'جيد': return 'info';
      case 'متوسط': return 'warning';
      default: return 'error';
    }
  }
}
