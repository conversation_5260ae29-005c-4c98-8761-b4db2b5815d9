import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../core/services/database_service.dart';
import '../../../features/users/services/users_service.dart';
import '../../../features/locations/services/location_service.dart';
import '../../../features/devices/services/devices_service.dart';
import '../models/dashboard_stats_model.dart';

/// مزود إحصائيات لوحة المراقبة
final dashboardStatsProvider = FutureProvider<DashboardStats>((ref) async {
  final databaseService = ref.read(databaseServiceProvider);
  final usersService = ref.read(usersServiceProvider);
  final locationService = ref.read(locationServiceProvider);
  final devicesService = ref.read(devicesServiceProvider);

  try {
    // الحصول على الإحصائيات العامة
    final generalStats = await databaseService.getGeneralStats();
    
    // الحصول على إحصائيات المواقع
    final locationStats = await locationService.getLocationStats();
    
    // الحصول على إحصائيات الأجهزة (من البيانات الوهمية حالياً)
    final devices = await devicesService.getDevices();
    final trustedDevices = devices.where((d) => d.trustLevel == DeviceTrustLevel.trusted).length;
    
    // حساب إحصائيات إضافية
    final totalMedia = generalStats['total_photos'] + generalStats['total_videos'];
    
    return DashboardStats(
      // إحصائيات المستخدمين
      totalUsers: generalStats['total_users'] ?? 0,
      activeUsers: generalStats['active_users'] ?? 0,
      newUsersToday: 0, // يمكن حسابها لاحقاً
      newUsersThisWeek: 0,
      
      // إحصائيات الوسائط
      totalPhotos: generalStats['total_photos'] ?? 0,
      totalVideos: generalStats['total_videos'] ?? 0,
      totalMedia: totalMedia,
      photosToday: 0, // يمكن حسابها لاحقاً
      videosToday: 0,
      
      // إحصائيات الأجهزة
      totalDevices: generalStats['total_devices'] ?? 0,
      trustedDevices: trustedDevices,
      blockedDevices: 0, // يمكن حسابها لاحقاً
      
      // إحصائيات المواقع
      totalLocations: locationStats['total_locations'] ?? 0,
      activeLocations: locationStats['active_locations'] ?? 0,
      uLocations: locationStats['u_locations'] ?? 0,
      cLocations: locationStats['c_locations'] ?? 0,
      locationsWithMedia: locationStats['locations_with_media'] ?? 0,
      
      // إحصائيات التخزين
      totalStorageGB: 0.0, // يمكن حسابها لاحقاً
      usedStorageGB: 0.0,
      storageUsagePercentage: 0.0,
      
      // معدلات الاستخدام
      userActivityRate: generalStats['total_users'] > 0 
          ? (generalStats['active_users'] / generalStats['total_users'] * 100)
          : 0.0,
      locationUsageRate: double.tryParse(locationStats['usage_rate'] ?? '0.0') ?? 0.0,
      deviceTrustRate: generalStats['total_devices'] > 0 
          ? (trustedDevices / generalStats['total_devices'] * 100)
          : 0.0,
      
      // آخر تحديث
      lastUpdated: DateTime.now(),
    );
  } catch (e) {
    // في حالة الفشل، إرجاع إحصائيات افتراضية
    return DashboardStats.empty();
  }
});

/// مزود إحصائيات النشاط اليومي
final dailyActivityProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  try {
    // يمكن إضافة استعلامات للنشاط اليومي هنا
    return {
      'photos_uploaded_today': 0,
      'videos_uploaded_today': 0,
      'new_users_today': 0,
      'active_users_today': 0,
      'login_attempts_today': 0,
      'successful_logins_today': 0,
    };
  } catch (e) {
    return {};
  }
});

/// مزود إحصائيات الأسبوع الحالي
final weeklyStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  try {
    // يمكن إضافة استعلامات للإحصائيات الأسبوعية هنا
    return {
      'photos_this_week': 0,
      'videos_this_week': 0,
      'new_users_this_week': 0,
      'active_locations_this_week': 0,
    };
  } catch (e) {
    return {};
  }
});

/// مزود أكثر المواقع استخداماً
final topLocationsProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  try {
    final locationService = ref.read(locationServiceProvider);
    final topLocations = await locationService.getTopUsedLocations(limit: 5);
    
    return topLocations.map((location) => {
      'location_code': location.locationCode,
      'location_name': location.locationNameAr,
      'total_media': location.totalMedia,
      'total_photos': location.totalPhotos,
      'total_videos': location.totalVideos,
      'last_used': location.lastUsedAt?.toIso8601String(),
    }).toList();
  } catch (e) {
    return [];
  }
});

/// مزود النشاط الحديث
final recentActivityProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  try {
    // يمكن إضافة استعلامات للنشاط الحديث هنا
    // مثل آخر الصور المرفوعة، المستخدمين الجدد، إلخ
    return [
      {
        'type': 'user_joined',
        'title': 'مستخدم جديد انضم',
        'subtitle': 'أحمد محمد - منذ 5 دقائق',
        'icon': 'person_add',
        'color': 'success',
        'timestamp': DateTime.now().subtract(const Duration(minutes: 5)).toIso8601String(),
      },
      {
        'type': 'media_uploaded',
        'title': 'تم رفع 15 صورة جديدة',
        'subtitle': 'الموقع U105 - منذ 12 دقيقة',
        'icon': 'photo_library',
        'color': 'info',
        'timestamp': DateTime.now().subtract(const Duration(minutes: 12)).toIso8601String(),
      },
      {
        'type': 'device_verified',
        'title': 'تم التحقق من جهاز جديد',
        'subtitle': 'iPhone 15 Pro - منذ 25 دقيقة',
        'icon': 'security',
        'color': 'warning',
        'timestamp': DateTime.now().subtract(const Duration(minutes: 25)).toIso8601String(),
      },
    ];
  } catch (e) {
    return [];
  }
});

/// مزود بيانات الرسم البياني للنشاط الأسبوعي
final weeklyActivityChartProvider = FutureProvider<Map<String, List<double>>>((ref) async {
  try {
    // بيانات وهمية للرسم البياني
    // يمكن استبدالها ببيانات فعلية من قاعدة البيانات
    return {
      'photos': [120, 150, 180, 200, 170, 190, 220],
      'videos': [30, 45, 50, 60, 40, 55, 65],
      'users': [10, 15, 12, 18, 20, 16, 22],
      'days': [1, 2, 3, 4, 5, 6, 7], // أيام الأسبوع
    };
  } catch (e) {
    return {
      'photos': [0, 0, 0, 0, 0, 0, 0],
      'videos': [0, 0, 0, 0, 0, 0, 0],
      'users': [0, 0, 0, 0, 0, 0, 0],
      'days': [1, 2, 3, 4, 5, 6, 7],
    };
  }
});

/// مزود إحصائيات الأداء
final performanceStatsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  try {
    final databaseService = ref.read(databaseServiceProvider);
    
    // اختبار سرعة الاتصال بقاعدة البيانات
    final startTime = DateTime.now();
    final isConnected = await databaseService.testConnection();
    final endTime = DateTime.now();
    final responseTime = endTime.difference(startTime).inMilliseconds;
    
    return {
      'database_connected': isConnected,
      'response_time_ms': responseTime,
      'status': isConnected ? 'healthy' : 'error',
      'uptime_percentage': 99.5, // يمكن حسابها من سجلات النظام
      'last_backup': DateTime.now().subtract(const Duration(hours: 6)).toIso8601String(),
      'system_load': 'low', // يمكن حسابها من مراقبة النظام
    };
  } catch (e) {
    return {
      'database_connected': false,
      'response_time_ms': 0,
      'status': 'error',
      'uptime_percentage': 0.0,
      'last_backup': null,
      'system_load': 'unknown',
    };
  }
});

/// مزود تحديث تلقائي للإحصائيات
final autoRefreshProvider = StateProvider<bool>((ref) => true);

/// مزود فترة التحديث (بالثواني)
final refreshIntervalProvider = StateProvider<int>((ref) => 30);

/// مزود آخر وقت تحديث
final lastRefreshProvider = StateProvider<DateTime?>((ref) => null);
