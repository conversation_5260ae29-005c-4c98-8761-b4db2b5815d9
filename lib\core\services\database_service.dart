import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../shared/models/photo_model.dart';
import '../../shared/models/video_model.dart';
import '../../features/locations/models/location_model.dart';
import '../../features/users/models/user_model.dart';
import '../../features/devices/models/device_model.dart';

/// مزود خدمة قاعدة البيانات
final databaseServiceProvider = Provider<DatabaseService>((ref) {
  return DatabaseService();
});

/// خدمة قاعدة البيانات المتقدمة
class DatabaseService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // ==================== المستخدمين ====================

  /// الحصول على قائمة المستخدمين
  Future<List<UserModel>> getUsers({
    int? limit,
    int? offset,
    String? searchQuery,
    bool? isActive,
    String? sortBy,
    bool sortDescending = false,
  }) async {
    try {
      var query = _supabase.from('users').select('*');

      // تطبيق البحث
      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.or('full_name.ilike.%$searchQuery%,email.ilike.%$searchQuery%,username.ilike.%$searchQuery%,national_id.ilike.%$searchQuery%');
      }

      // تطبيق فلتر الحالة
      if (isActive != null) {
        query = query.eq('is_active', isActive);
      }

      // تطبيق الترتيب
      if (sortBy != null) {
        query = query.order(sortBy, ascending: !sortDescending);
      } else {
        query = query.order('created_at', ascending: false);
      }

      // تطبيق التصفح
      if (offset != null) {
        query = query.range(offset, offset + (limit ?? 20) - 1);
      } else if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;
      return response.map<UserModel>((json) => UserModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في تحميل المستخدمين: $e');
    }
  }

  /// الحصول على مستخدم بالمعرف
  Future<UserModel?> getUserById(String userId) async {
    try {
      final response = await _supabase
          .from('users')
          .select('*')
          .eq('id', userId)
          .maybeSingle();

      return response != null ? UserModel.fromJson(response) : null;
    } catch (e) {
      throw Exception('فشل في تحميل المستخدم: $e');
    }
  }

  /// إنشاء مستخدم جديد
  Future<UserModel> createUser(UserModel user) async {
    try {
      final response = await _supabase
          .from('users')
          .insert(user.toJson())
          .select()
          .single();

      return UserModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في إنشاء المستخدم: $e');
    }
  }

  /// تحديث مستخدم
  Future<UserModel> updateUser(String userId, Map<String, dynamic> updates) async {
    try {
      updates['updated_at'] = DateTime.now().toIso8601String();
      
      final response = await _supabase
          .from('users')
          .update(updates)
          .eq('id', userId)
          .select()
          .single();

      return UserModel.fromJson(response);
    } catch (e) {
      throw Exception('فشل في تحديث المستخدم: $e');
    }
  }

  /// حذف مستخدم
  Future<void> deleteUser(String userId) async {
    try {
      await _supabase.from('users').delete().eq('id', userId);
    } catch (e) {
      throw Exception('فشل في حذف المستخدم: $e');
    }
  }

  // ==================== المواقع ====================

  /// الحصول على قائمة المواقع
  Future<List<LocationModel>> getLocations({
    String? locationType,
    bool? isActive,
    String? searchQuery,
    String? sortBy,
    bool sortDescending = false,
  }) async {
    try {
      var query = _supabase.from('locations').select('*');

      // تطبيق فلتر النوع
      if (locationType != null) {
        query = query.eq('location_type', locationType);
      }

      // تطبيق فلتر الحالة
      if (isActive != null) {
        query = query.eq('is_active', isActive);
      }

      // تطبيق البحث
      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.or('location_code.ilike.%$searchQuery%,location_name_ar.ilike.%$searchQuery%,location_name_en.ilike.%$searchQuery%');
      }

      // تطبيق الترتيب
      if (sortBy != null) {
        query = query.order(sortBy, ascending: !sortDescending);
      } else {
        query = query.order('sort_order', ascending: true);
      }

      final response = await query;
      return response.map<LocationModel>((json) => LocationModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في تحميل المواقع: $e');
    }
  }

  /// تحديث إحصائيات الموقع
  Future<void> updateLocationStats(String locationCode) async {
    try {
      // تحديث إحصائيات الصور
      final photosCount = await _supabase
          .from('photos')
          .select('id')
          .eq('full_location_code', locationCode)
          .eq('status', 'active')
          .count();

      // تحديث إحصائيات الفيديوهات
      final videosCount = await _supabase
          .from('videos')
          .select('id')
          .eq('full_location_code', locationCode)
          .eq('status', 'active')
          .count();

      // تحديث الموقع
      await _supabase
          .from('locations')
          .update({
            'total_photos': photosCount.count,
            'total_videos': videosCount.count,
            'last_used_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('location_code', locationCode);
    } catch (e) {
      throw Exception('فشل في تحديث إحصائيات الموقع: $e');
    }
  }

  // ==================== الصور ====================

  /// الحصول على قائمة الصور
  Future<List<PhotoModel>> getPhotos({
    int? limit,
    int? offset,
    String? userId,
    String? locationType,
    String? locationNumber,
    PhotoStatus? status,
    DateTime? captureAfter,
    DateTime? captureBefore,
    String? sortBy,
    bool sortDescending = true,
  }) async {
    try {
      var query = _supabase.from('photos').select('*');

      // تطبيق الفلاتر
      if (userId != null) {
        query = query.eq('user_id', userId);
      }

      if (locationType != null) {
        query = query.eq('location_type', locationType);
      }

      if (locationNumber != null) {
        query = query.eq('location_number', locationNumber);
      }

      if (status != null) {
        query = query.eq('status', status.name);
      }

      if (captureAfter != null) {
        query = query.gte('capture_timestamp', captureAfter.toIso8601String());
      }

      if (captureBefore != null) {
        query = query.lte('capture_timestamp', captureBefore.toIso8601String());
      }

      // تطبيق الترتيب
      if (sortBy != null) {
        query = query.order(sortBy, ascending: !sortDescending);
      } else {
        query = query.order('capture_timestamp', ascending: false);
      }

      // تطبيق التصفح
      if (offset != null) {
        query = query.range(offset, offset + (limit ?? 20) - 1);
      } else if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;
      return response.map<PhotoModel>((json) => PhotoModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في تحميل الصور: $e');
    }
  }

  // ==================== الفيديوهات ====================

  /// الحصول على قائمة الفيديوهات
  Future<List<VideoModel>> getVideos({
    int? limit,
    int? offset,
    String? userId,
    String? locationType,
    String? locationNumber,
    VideoStatus? status,
    DateTime? captureAfter,
    DateTime? captureBefore,
    String? sortBy,
    bool sortDescending = true,
  }) async {
    try {
      var query = _supabase.from('videos').select('*');

      // تطبيق الفلاتر (مشابه للصور)
      if (userId != null) {
        query = query.eq('user_id', userId);
      }

      if (locationType != null) {
        query = query.eq('location_type', locationType);
      }

      if (locationNumber != null) {
        query = query.eq('location_number', locationNumber);
      }

      if (status != null) {
        query = query.eq('status', status.name);
      }

      if (captureAfter != null) {
        query = query.gte('capture_timestamp', captureAfter.toIso8601String());
      }

      if (captureBefore != null) {
        query = query.lte('capture_timestamp', captureBefore.toIso8601String());
      }

      // تطبيق الترتيب
      if (sortBy != null) {
        query = query.order(sortBy, ascending: !sortDescending);
      } else {
        query = query.order('capture_timestamp', ascending: false);
      }

      // تطبيق التصفح
      if (offset != null) {
        query = query.range(offset, offset + (limit ?? 20) - 1);
      } else if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;
      return response.map<VideoModel>((json) => VideoModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في تحميل الفيديوهات: $e');
    }
  }

  // ==================== الأجهزة ====================

  /// الحصول على قائمة الأجهزة
  Future<List<DeviceModel>> getDevices({
    String? userId,
    DeviceTrustLevel? trustLevel,
    bool? isBlocked,
    String? deviceType,
    String? sortBy,
    bool sortDescending = false,
  }) async {
    try {
      var query = _supabase.from('devices').select('*');

      // تطبيق الفلاتر
      if (userId != null) {
        query = query.eq('user_id', userId);
      }

      if (trustLevel != null) {
        query = query.eq('trust_level', trustLevel.index + 1);
      }

      if (isBlocked != null) {
        query = query.eq('is_blocked', isBlocked);
      }

      if (deviceType != null) {
        query = query.eq('device_type', deviceType);
      }

      // تطبيق الترتيب
      if (sortBy != null) {
        query = query.order(sortBy, ascending: !sortDescending);
      } else {
        query = query.order('last_seen_at', ascending: false);
      }

      final response = await query;
      return response.map<DeviceModel>((json) => DeviceModel.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في تحميل الأجهزة: $e');
    }
  }

  // ==================== الإحصائيات ====================

  /// الحصول على إحصائيات عامة
  Future<Map<String, dynamic>> getGeneralStats() async {
    try {
      // إحصائيات المستخدمين
      final totalUsers = await _supabase.from('users').select('id').count();
      final activeUsers = await _supabase.from('users').select('id').eq('is_active', true).count();

      // إحصائيات الوسائط
      final totalPhotos = await _supabase.from('photos').select('id').eq('status', 'active').count();
      final totalVideos = await _supabase.from('videos').select('id').eq('status', 'active').count();

      // إحصائيات الأجهزة
      final totalDevices = await _supabase.from('devices').select('id').count();
      final trustedDevices = await _supabase.from('devices').select('id').eq('trust_level', 4).count();

      // إحصائيات المواقع
      final activeLocations = await _supabase.from('locations').select('id').eq('is_active', true).count();

      return {
        'total_users': totalUsers.count,
        'active_users': activeUsers.count,
        'total_photos': totalPhotos.count,
        'total_videos': totalVideos.count,
        'total_media': totalPhotos.count + totalVideos.count,
        'total_devices': totalDevices.count,
        'trusted_devices': trustedDevices.count,
        'active_locations': activeLocations.count,
      };
    } catch (e) {
      throw Exception('فشل في تحميل الإحصائيات: $e');
    }
  }

  /// اختبار الاتصال بقاعدة البيانات
  Future<bool> testConnection() async {
    try {
      await _supabase.from('users').select('id').limit(1);
      return true;
    } catch (e) {
      return false;
    }
  }
}
