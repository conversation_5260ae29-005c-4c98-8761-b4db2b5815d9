import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/location_model.dart';
import '../../../core/services/database_service.dart';

/// مزود خدمة المواقع
final locationServiceProvider = Provider<LocationService>((ref) {
  return LocationService(ref.read(databaseServiceProvider));
});

/// خدمة إدارة المواقع - متوافقة مع نظام المواقع الجديد (70 موقع)
class LocationService {
  final DatabaseService _databaseService;
  
  LocationService(this._databaseService);
  
  /// الحصول على قائمة المواقع
  Future<List<LocationModel>> getLocations({
    String? locationType,
    bool? isActive,
    String? searchQuery,
    String? sortBy,
    bool sortDescending = false,
  }) async {
    try {
      // محاولة الحصول على البيانات الفعلية
      final locations = await _databaseService.getLocations(
        locationType: locationType,
        isActive: isActive,
        searchQuery: searchQuery,
        sortBy: sortBy,
        sortDescending: sortDescending,
      );
      
      return locations;
    } catch (error) {
      // في حالة فشل الاتصال، استخدم البيانات الوهمية
      print('تحذير: فشل في تحميل المواقع من قاعدة البيانات، استخدام البيانات الوهمية: $error');
      return _generateMockLocations();
    }
  }

  /// الحصول على موقع بالكود
  Future<LocationModel?> getLocationByCode(String locationCode) async {
    try {
      final locations = await getLocations();
      return locations.where((loc) => loc.locationCode == locationCode).firstOrNull;
    } catch (error) {
      throw Exception('فشل في تحميل الموقع: $error');
    }
  }

  /// تحديث إحصائيات الموقع
  Future<void> updateLocationStats(String locationCode) async {
    try {
      await _databaseService.updateLocationStats(locationCode);
    } catch (error) {
      throw Exception('فشل في تحديث إحصائيات الموقع: $error');
    }
  }

  /// تحديث معلومات الموقع
  Future<LocationModel> updateLocation(String locationId, Map<String, dynamic> updates) async {
    try {
      // محاكاة تحديث الموقع
      await Future.delayed(const Duration(milliseconds: 500));
      final locations = await getLocations();
      final location = locations.firstWhere((l) => l.id == locationId);
      
      return location.copyWith(
        locationNameAr: updates['location_name_ar'] ?? location.locationNameAr,
        locationNameEn: updates['location_name_en'] ?? location.locationNameEn,
        descriptionAr: updates['description_ar'] ?? location.descriptionAr,
        descriptionEn: updates['description_en'] ?? location.descriptionEn,
        isActive: updates['is_active'] ?? location.isActive,
        isAvailable: updates['is_available'] ?? location.isAvailable,
        category: updates['category'] ?? location.category,
        department: updates['department'] ?? location.department,
        updatedAt: DateTime.now(),
      );
    } catch (error) {
      throw Exception('فشل في تحديث الموقع: $error');
    }
  }

  /// تغيير حالة الموقع
  Future<LocationModel> toggleLocationStatus(String locationId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 300));
      final locations = await getLocations();
      final location = locations.firstWhere((l) => l.id == locationId);
      
      return location.copyWith(
        isActive: !location.isActive,
        updatedAt: DateTime.now(),
      );
    } catch (error) {
      throw Exception('فشل في تغيير حالة الموقع: $error');
    }
  }

  /// الحصول على إحصائيات المواقع
  Future<Map<String, dynamic>> getLocationStats() async {
    try {
      final locations = await getLocations();
      
      final totalLocations = locations.length;
      final activeLocations = locations.where((l) => l.isActive).length;
      final uLocations = locations.where((l) => l.locationType == 'U').length;
      final cLocations = locations.where((l) => l.locationType == 'C').length;
      final locationsWithMedia = locations.where((l) => l.totalMedia > 0).length;
      final recentlyUsed = locations.where((l) => l.hasRecentActivity).length;
      
      final totalPhotos = locations.fold<int>(0, (sum, l) => sum + l.totalPhotos);
      final totalVideos = locations.fold<int>(0, (sum, l) => sum + l.totalVideos);
      
      return {
        'total_locations': totalLocations,
        'active_locations': activeLocations,
        'u_locations': uLocations,
        'c_locations': cLocations,
        'locations_with_media': locationsWithMedia,
        'recently_used': recentlyUsed,
        'total_photos': totalPhotos,
        'total_videos': totalVideos,
        'total_media': totalPhotos + totalVideos,
        'usage_rate': totalLocations > 0 
            ? (locationsWithMedia / totalLocations * 100).toStringAsFixed(1)
            : '0.0',
      };
    } catch (error) {
      return {
        'total_locations': 0,
        'active_locations': 0,
        'u_locations': 0,
        'c_locations': 0,
        'locations_with_media': 0,
        'recently_used': 0,
        'total_photos': 0,
        'total_videos': 0,
        'total_media': 0,
        'usage_rate': '0.0',
      };
    }
  }

  /// الحصول على أكثر المواقع استخداماً
  Future<List<LocationModel>> getTopUsedLocations({int limit = 10}) async {
    try {
      final locations = await getLocations();
      
      // ترتيب حسب إجمالي الوسائط
      locations.sort((a, b) => b.totalMedia.compareTo(a.totalMedia));
      
      return locations.take(limit).toList();
    } catch (error) {
      return [];
    }
  }

  /// توليد بيانات وهمية للمواقع (70 موقع)
  List<LocationModel> _generateMockLocations() {
    final locations = <LocationModel>[];
    
    // إنشاء مواقع U (U101-U125)
    for (int i = 101; i <= 125; i++) {
      final locationCode = 'U$i';
      final id = 'u_$i';
      
      locations.add(LocationModel(
        id: id,
        locationCode: locationCode,
        locationType: 'U',
        locationNumber: i.toString(),
        locationNameAr: 'موقع يو $i',
        locationNameEn: 'U Location $i',
        descriptionAr: 'وصف الموقع $locationCode',
        descriptionEn: 'Description for location $locationCode',
        sortOrder: i,
        category: i % 3 == 0 ? 'مكاتب' : (i % 3 == 1 ? 'قاعات' : 'مختبرات'),
        department: i % 4 == 0 ? 'تقنية المعلومات' : (i % 4 == 1 ? 'الهندسة' : (i % 4 == 2 ? 'الإدارة' : 'الخدمات')),
        isActive: i % 10 != 0, // 90% نشط
        isAvailable: i % 8 != 0, // 87.5% متاح
        totalPhotos: (i * 23) % 500,
        totalVideos: (i * 7) % 100,
        lastUsedAt: i % 5 == 0 ? null : DateTime.now().subtract(Duration(days: i % 30)),
        createdAt: DateTime.now().subtract(Duration(days: 365 - i)),
        updatedAt: DateTime.now().subtract(Duration(days: i % 30)),
      ));
    }
    
    // إنشاء مواقع C (C101-C145)
    for (int i = 101; i <= 145; i++) {
      final locationCode = 'C$i';
      final id = 'c_$i';
      
      locations.add(LocationModel(
        id: id,
        locationCode: locationCode,
        locationType: 'C',
        locationNumber: i.toString(),
        locationNameAr: 'موقع سي $i',
        locationNameEn: 'C Location $i',
        descriptionAr: 'وصف الموقع $locationCode',
        descriptionEn: 'Description for location $locationCode',
        sortOrder: i + 100, // بعد مواقع U
        category: i % 3 == 0 ? 'فصول دراسية' : (i % 3 == 1 ? 'معامل' : 'ورش'),
        department: i % 5 == 0 ? 'العلوم' : (i % 5 == 1 ? 'الرياضيات' : (i % 5 == 2 ? 'الفيزياء' : (i % 5 == 3 ? 'الكيمياء' : 'الأحياء'))),
        isActive: i % 12 != 0, // 91.7% نشط
        isAvailable: i % 9 != 0, // 88.9% متاح
        totalPhotos: (i * 31) % 800,
        totalVideos: (i * 11) % 150,
        lastUsedAt: i % 6 == 0 ? null : DateTime.now().subtract(Duration(days: i % 45)),
        createdAt: DateTime.now().subtract(Duration(days: 300 - (i - 100))),
        updatedAt: DateTime.now().subtract(Duration(days: i % 20)),
      ));
    }
    
    return locations;
  }

  /// البحث في المواقع
  Future<List<LocationModel>> searchLocations(String query) async {
    try {
      final locations = await getLocations();
      
      if (query.isEmpty) return locations;
      
      final lowerQuery = query.toLowerCase();
      
      return locations.where((location) {
        return location.locationCode.toLowerCase().contains(lowerQuery) ||
               location.locationNameAr.toLowerCase().contains(lowerQuery) ||
               (location.locationNameEn?.toLowerCase().contains(lowerQuery) ?? false) ||
               (location.category?.toLowerCase().contains(lowerQuery) ?? false) ||
               (location.department?.toLowerCase().contains(lowerQuery) ?? false);
      }).toList();
    } catch (error) {
      throw Exception('فشل في البحث في المواقع: $error');
    }
  }

  /// فلترة المواقع حسب النوع
  Future<List<LocationModel>> getLocationsByType(LocationType type) async {
    try {
      final locations = await getLocations();
      return locations.where((l) => l.locationType == type.name).toList();
    } catch (error) {
      throw Exception('فشل في فلترة المواقع: $error');
    }
  }

  /// الحصول على المواقع النشطة حديثاً
  Future<List<LocationModel>> getRecentlyActiveLocations({int days = 7}) async {
    try {
      final locations = await getLocations();
      final cutoffDate = DateTime.now().subtract(Duration(days: days));
      
      return locations.where((location) {
        return location.lastUsedAt != null && 
               location.lastUsedAt!.isAfter(cutoffDate);
      }).toList();
    } catch (error) {
      throw Exception('فشل في تحميل المواقع النشطة: $error');
    }
  }
}
