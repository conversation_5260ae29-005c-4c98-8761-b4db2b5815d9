/// نموذج بيانات الجهاز
class DeviceModel {
  final String id;
  final String deviceName;
  final String deviceType;
  final String deviceModel;
  final String operatingSystem;
  final String osVersion;
  final String fingerprint;
  final DeviceTrustLevel trustLevel;
  final String userId;
  final String userName;
  final DateTime firstSeenAt;
  final DateTime lastSeenAt;
  final int authAttempts;
  final int successfulAuths;
  final int failedAuths;
  final bool isBlocked;
  final String? blockReason;
  final DateTime? blockedAt;
  final String? notes;
  final Map<String, dynamic> deviceInfo;

  const DeviceModel({
    required this.id,
    required this.deviceName,
    required this.deviceType,
    required this.deviceModel,
    required this.operatingSystem,
    required this.osVersion,
    required this.fingerprint,
    required this.trustLevel,
    required this.userId,
    required this.userName,
    required this.firstSeenAt,
    required this.lastSeenAt,
    required this.authAttempts,
    required this.successfulAuths,
    required this.failedAuths,
    required this.isBlocked,
    this.blockReason,
    this.blockedAt,
    this.notes,
    this.deviceInfo = const {},
  });

  DeviceModel copyWith({
    String? id,
    String? deviceName,
    String? deviceType,
    String? deviceModel,
    String? operatingSystem,
    String? osVersion,
    String? fingerprint,
    DeviceTrustLevel? trustLevel,
    String? userId,
    String? userName,
    DateTime? firstSeenAt,
    DateTime? lastSeenAt,
    int? authAttempts,
    int? successfulAuths,
    int? failedAuths,
    bool? isBlocked,
    String? blockReason,
    DateTime? blockedAt,
    String? notes,
    Map<String, dynamic>? deviceInfo,
  }) {
    return DeviceModel(
      id: id ?? this.id,
      deviceName: deviceName ?? this.deviceName,
      deviceType: deviceType ?? this.deviceType,
      deviceModel: deviceModel ?? this.deviceModel,
      operatingSystem: operatingSystem ?? this.operatingSystem,
      osVersion: osVersion ?? this.osVersion,
      fingerprint: fingerprint ?? this.fingerprint,
      trustLevel: trustLevel ?? this.trustLevel,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      firstSeenAt: firstSeenAt ?? this.firstSeenAt,
      lastSeenAt: lastSeenAt ?? this.lastSeenAt,
      authAttempts: authAttempts ?? this.authAttempts,
      successfulAuths: successfulAuths ?? this.successfulAuths,
      failedAuths: failedAuths ?? this.failedAuths,
      isBlocked: isBlocked ?? this.isBlocked,
      blockReason: blockReason ?? this.blockReason,
      blockedAt: blockedAt ?? this.blockedAt,
      notes: notes ?? this.notes,
      deviceInfo: deviceInfo ?? this.deviceInfo,
    );
  }

  factory DeviceModel.fromJson(Map<String, dynamic> json) {
    return DeviceModel(
      id: json['id'] as String,
      deviceName: json['device_name'] as String? ?? 'Unknown Device',
      deviceType: json['device_type'] as String? ?? 'Unknown',
      deviceModel: json['device_model'] as String? ?? 'Unknown',
      operatingSystem: json['operating_system'] as String? ?? 'Unknown',
      osVersion: json['os_version'] as String? ?? 'Unknown',
      fingerprint: json['device_fingerprint'] as String,
      trustLevel: DeviceTrustLevel.values.firstWhere(
        (e) => e.index + 1 == (json['trust_level'] as int),
        orElse: () => DeviceTrustLevel.suspicious,
      ),
      userId: json['user_id'] as String,
      userName: json['user_name'] as String? ?? 'Unknown User',
      firstSeenAt: DateTime.parse(json['first_seen_at'] as String),
      lastSeenAt: DateTime.parse(json['last_seen_at'] as String),
      authAttempts: json['auth_attempts'] as int? ?? 0,
      successfulAuths: json['successful_auths'] as int? ?? 0,
      failedAuths: json['failed_auths'] as int? ?? 0,
      isBlocked: json['is_blocked'] as bool? ?? false,
      blockReason: json['block_reason'] as String?,
      blockedAt: json['blocked_at'] != null ? DateTime.parse(json['blocked_at'] as String) : null,
      notes: json['notes'] as String?,
      deviceInfo: Map<String, dynamic>.from(json['device_info'] as Map? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'device_name': deviceName,
      'device_type': deviceType,
      'device_model': deviceModel,
      'operating_system': operatingSystem,
      'os_version': osVersion,
      'device_fingerprint': fingerprint,
      'trust_level': trustLevel.index + 1,
      'user_id': userId,
      'user_name': userName,
      'first_seen_at': firstSeenAt.toIso8601String(),
      'last_seen_at': lastSeenAt.toIso8601String(),
      'auth_attempts': authAttempts,
      'successful_auths': successfulAuths,
      'failed_auths': failedAuths,
      'is_blocked': isBlocked,
      'block_reason': blockReason,
      'blocked_at': blockedAt?.toIso8601String(),
      'notes': notes,
      'device_info': deviceInfo,
    };
  }
}

/// مستويات ثقة الجهاز
enum DeviceTrustLevel {
  trusted,    // موثوق
  verified,   // محقق
  suspicious, // مشكوك
  blocked,    // محظور
}

extension DeviceTrustLevelExtension on DeviceTrustLevel {
  String get displayName {
    switch (this) {
      case DeviceTrustLevel.trusted:
        return 'موثوق';
      case DeviceTrustLevel.verified:
        return 'محقق';
      case DeviceTrustLevel.suspicious:
        return 'مشكوك';
      case DeviceTrustLevel.blocked:
        return 'محظور';
    }
  }

  String get description {
    switch (this) {
      case DeviceTrustLevel.trusted:
        return 'جهاز موثوق بالكامل';
      case DeviceTrustLevel.verified:
        return 'جهاز تم التحقق منه';
      case DeviceTrustLevel.suspicious:
        return 'جهاز مشكوك فيه';
      case DeviceTrustLevel.blocked:
        return 'جهاز محظور';
    }
  }
}

/// فلاتر البحث للأجهزة
class DeviceFilters {
  final String? searchQuery;
  final DeviceTrustLevel? trustLevel;
  final String? deviceType;
  final String? operatingSystem;
  final bool? isBlocked;
  final DateTime? firstSeenAfter;
  final DateTime? firstSeenBefore;
  final DateTime? lastSeenAfter;
  final DateTime? lastSeenBefore;
  final int pageSize;
  final int pageOffset;
  final String sortBy;
  final bool sortDescending;

  const DeviceFilters({
    this.searchQuery,
    this.trustLevel,
    this.deviceType,
    this.operatingSystem,
    this.isBlocked,
    this.firstSeenAfter,
    this.firstSeenBefore,
    this.lastSeenAfter,
    this.lastSeenBefore,
    this.pageSize = 20,
    this.pageOffset = 0,
    this.sortBy = 'lastSeenAt',
    this.sortDescending = true,
  });

  DeviceFilters copyWith({
    String? searchQuery,
    DeviceTrustLevel? trustLevel,
    String? deviceType,
    String? operatingSystem,
    bool? isBlocked,
    DateTime? firstSeenAfter,
    DateTime? firstSeenBefore,
    DateTime? lastSeenAfter,
    DateTime? lastSeenBefore,
    int? pageSize,
    int? pageOffset,
    String? sortBy,
    bool? sortDescending,
  }) {
    return DeviceFilters(
      searchQuery: searchQuery ?? this.searchQuery,
      trustLevel: trustLevel ?? this.trustLevel,
      deviceType: deviceType ?? this.deviceType,
      operatingSystem: operatingSystem ?? this.operatingSystem,
      isBlocked: isBlocked ?? this.isBlocked,
      firstSeenAfter: firstSeenAfter ?? this.firstSeenAfter,
      firstSeenBefore: firstSeenBefore ?? this.firstSeenBefore,
      lastSeenAfter: lastSeenAfter ?? this.lastSeenAfter,
      lastSeenBefore: lastSeenBefore ?? this.lastSeenBefore,
      pageSize: pageSize ?? this.pageSize,
      pageOffset: pageOffset ?? this.pageOffset,
      sortBy: sortBy ?? this.sortBy,
      sortDescending: sortDescending ?? this.sortDescending,
    );
  }
}

/// إحصائيات الأجهزة
class DeviceStats {
  final int totalDevices;
  final int trustedDevices;
  final int verifiedDevices;
  final int suspiciousDevices;
  final int blockedDevices;
  final int activeDevices;
  final int newDevicesToday;
  final int newDevicesThisWeek;
  final int newDevicesThisMonth;
  final Map<String, int> deviceTypeStats;
  final Map<String, int> osStats;

  const DeviceStats({
    required this.totalDevices,
    required this.trustedDevices,
    required this.verifiedDevices,
    required this.suspiciousDevices,
    required this.blockedDevices,
    required this.activeDevices,
    required this.newDevicesToday,
    required this.newDevicesThisWeek,
    required this.newDevicesThisMonth,
    required this.deviceTypeStats,
    required this.osStats,
  });
}
