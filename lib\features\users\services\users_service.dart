import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../../../core/services/database_service.dart';

/// مزود خدمة المستخدمين
final usersServiceProvider = Provider<UsersService>((ref) {
  return UsersService(ref.read(databaseServiceProvider));
});

/// خدمة إدارة المستخدمين
class UsersService {
  final DatabaseService _databaseService;

  UsersService(this._databaseService);

  /// الحصول على قائمة المستخدمين
  Future<List<UserModel>> getUsers({
    int? limit,
    int? offset,
    String? searchQuery,
    bool? isActive,
    String? sortBy,
    bool sortDescending = false,
  }) async {
    try {
      // محاولة الحصول على البيانات الفعلية
      final users = await _databaseService.getUsers(
        limit: limit,
        offset: offset,
        searchQuery: searchQuery,
        isActive: isActive,
        sortBy: sortBy,
        sortDescending: sortDescending,
      );

      return users;
    } catch (error) {
      // في حالة فشل الاتصال، استخدم البيانات الوهمية
      print('تحذير: فشل في تحميل المستخدمين من قاعدة البيانات، استخدام البيانات الوهمية: $error');
      return _generateMockUsers();
    }
  }
  
  /// إنشاء مستخدم جديد
  Future<UserModel> createUser(UserModel user) async {
    try {
      // محاولة إنشاء المستخدم في قاعدة البيانات
      return await _databaseService.createUser(user);
    } catch (error) {
      // في حالة الفشل، محاكاة إنشاء مستخدم جديد
      await Future.delayed(const Duration(seconds: 1));
      return user.copyWith(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }
  }
  
  /// تحديث مستخدم
  Future<UserModel> updateUser(UserModel user) async {
    try {
      // محاكاة تحديث المستخدم
      await Future.delayed(const Duration(milliseconds: 500));
      return user;
    } catch (error) {
      throw Exception('فشل في تحديث المستخدم: $error');
    }
  }
  
  /// حذف مستخدم
  Future<void> deleteUser(String userId) async {
    try {
      // محاكاة حذف المستخدم
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (error) {
      throw Exception('فشل في حذف المستخدم: $error');
    }
  }
  
  /// إعادة تعيين كلمة المرور
  Future<void> resetUserPassword(String userId) async {
    try {
      // محاكاة إعادة تعيين كلمة المرور
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (error) {
      throw Exception('فشل في إعادة تعيين كلمة المرور: $error');
    }
  }
  
  /// الحصول على إحصائيات المستخدمين
  Future<UserStats> getUserStats() async {
    try {
      // محاكاة إحصائيات المستخدمين
      await Future.delayed(const Duration(milliseconds: 300));
      return const UserStats(
        totalUsers: 1234,
        activeUsers: 1156,
        inactiveUsers: 78,
        suspendedUsers: 12,
        pendingUsers: 23,
        adminUsers: 5,
        regularUsers: 1229,
        usersWithDevices: 1098,
        usersWithMedia: 987,
        averageStorageUsage: 2.4,
        newUsersToday: 15,
        newUsersThisWeek: 89,
        newUsersThisMonth: 234,
      );
    } catch (error) {
      throw Exception('فشل في تحميل الإحصائيات: $error');
    }
  }
  
  /// توليد بيانات وهمية للاختبار
  List<UserModel> _generateMockUsers() {
    final users = <UserModel>[];
    final names = [
      'أحمد محمد علي',
      'فاطمة أحمد حسن',
      'محمد عبدالله سالم',
      'نورا خالد محمد',
      'عبدالرحمن سعد أحمد',
      'مريم عبدالعزيز علي',
      'خالد محمود حسن',
      'سارة أحمد محمد',
      'عمر عبدالله خالد',
      'هند محمد سالم',
      'يوسف أحمد علي',
      'ليلى عبدالرحمن حسن',
      'سعد محمد أحمد',
      'رنا خالد عبدالله',
      'طارق سالم محمد',
    ];
    
    for (int i = 0; i < names.length; i++) {
      final name = names[i];
      final id = (i + 1).toString().padLeft(3, '0');
      users.add(UserModel(
        id: id,
        fullName: name,
        nationalId: '1${(1000000000 + i * 123456789).toString().substring(0, 9)}',
        email: '${name.split(' ')[0].toLowerCase()}@moonmemory.com',
        username: '${name.split(' ')[0].toLowerCase()}${i + 1}',
        passwordHash: 'hashed_password_${i + 1}',
        createdAt: DateTime.now().subtract(Duration(days: i * 7)),
        updatedAt: DateTime.now().subtract(Duration(days: i * 3)),
        lastLoginAt: i % 3 == 0 ? null : DateTime.now().subtract(Duration(hours: i * 2)),
        isActive: i % 8 != 0,
        isVerified: i % 4 != 0,
        totalPhotos: (i * 123) % 1000,
        totalVideos: (i * 45) % 200,
        totalStorageMb: (i * 234.5) % 5000,
        notes: i % 4 == 0 ? 'مستخدم مميز' : null,
        permissions: i < 5 ? ['read', 'write', 'delete'] : ['read'],
      ));
    }
    
    return users;
  }

  /// تفعيل/تعليق المستخدم
  Future<UserModel> toggleUserStatus(String userId) async {
    try {
      // محاكاة تغيير حالة المستخدم
      await Future.delayed(const Duration(milliseconds: 500));
      final users = await getUsers();
      final user = users.firstWhere((u) => u.id == userId);
      return user.copyWith(isActive: !user.isActive);
    } catch (error) {
      throw Exception('فشل في تغيير حالة المستخدم: $error');
    }
  }

  /// إعادة تعيين كلمة المرور (alias)
  Future<void> resetPassword(String userId) async {
    return resetUserPassword(userId);
  }
}
