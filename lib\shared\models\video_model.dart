/// نموذج بيانات الفيديو - متوافق مع قاعدة البيانات الجديدة
class VideoModel {
  final String id;
  final String userId;
  final String fileName;
  final String? storagePath;
  final String? videoUrl;
  final int? fileSizeBytes;
  final int? durationSeconds;
  final String? resolution;
  final int? fps;
  final String? codec;
  final int? bitrate;
  final String? locationType;
  final String? locationNumber;
  final String? fullLocationCode;
  final String username;
  final DateTime captureTimestamp;
  final DateTime uploadTimestamp;
  final int? sortOrder;
  final List<String> tags;
  final String? description;
  final Map<String, dynamic>? cameraSettings;
  final Map<String, double>? gpsCoordinates;
  final VideoStatus status;
  final DateTime createdAt;
  final DateTime updatedAt;

  const VideoModel({
    required this.id,
    required this.userId,
    required this.fileName,
    this.storagePath,
    this.videoUrl,
    this.fileSizeBytes,
    this.durationSeconds,
    this.resolution,
    this.fps,
    this.codec,
    this.bitrate,
    this.locationType,
    this.locationNumber,
    this.fullLocationCode,
    required this.username,
    required this.captureTimestamp,
    required this.uploadTimestamp,
    this.sortOrder,
    this.tags = const [],
    this.description,
    this.cameraSettings,
    this.gpsCoordinates,
    this.status = VideoStatus.active,
    required this.createdAt,
    required this.updatedAt,
  });

  VideoModel copyWith({
    String? id,
    String? userId,
    String? fileName,
    String? storagePath,
    String? videoUrl,
    int? fileSizeBytes,
    int? durationSeconds,
    String? resolution,
    int? fps,
    String? codec,
    int? bitrate,
    String? locationType,
    String? locationNumber,
    String? fullLocationCode,
    String? username,
    DateTime? captureTimestamp,
    DateTime? uploadTimestamp,
    int? sortOrder,
    List<String>? tags,
    String? description,
    Map<String, dynamic>? cameraSettings,
    Map<String, double>? gpsCoordinates,
    VideoStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return VideoModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      fileName: fileName ?? this.fileName,
      storagePath: storagePath ?? this.storagePath,
      videoUrl: videoUrl ?? this.videoUrl,
      fileSizeBytes: fileSizeBytes ?? this.fileSizeBytes,
      durationSeconds: durationSeconds ?? this.durationSeconds,
      resolution: resolution ?? this.resolution,
      fps: fps ?? this.fps,
      codec: codec ?? this.codec,
      bitrate: bitrate ?? this.bitrate,
      locationType: locationType ?? this.locationType,
      locationNumber: locationNumber ?? this.locationNumber,
      fullLocationCode: fullLocationCode ?? this.fullLocationCode,
      username: username ?? this.username,
      captureTimestamp: captureTimestamp ?? this.captureTimestamp,
      uploadTimestamp: uploadTimestamp ?? this.uploadTimestamp,
      sortOrder: sortOrder ?? this.sortOrder,
      tags: tags ?? this.tags,
      description: description ?? this.description,
      cameraSettings: cameraSettings ?? this.cameraSettings,
      gpsCoordinates: gpsCoordinates ?? this.gpsCoordinates,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  factory VideoModel.fromJson(Map<String, dynamic> json) {
    return VideoModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      fileName: json['file_name'] as String,
      storagePath: json['storage_path'] as String?,
      videoUrl: json['video_url'] as String?,
      fileSizeBytes: json['file_size_bytes'] as int?,
      durationSeconds: json['duration_seconds'] as int?,
      resolution: json['resolution'] as String?,
      fps: json['fps'] as int?,
      codec: json['codec'] as String?,
      bitrate: json['bitrate'] as int?,
      locationType: json['location_type'] as String?,
      locationNumber: json['location_number'] as String?,
      fullLocationCode: json['full_location_code'] as String?,
      username: json['username'] as String,
      captureTimestamp: DateTime.parse(json['capture_timestamp'] as String),
      uploadTimestamp: DateTime.parse(json['upload_timestamp'] as String),
      sortOrder: json['sort_order'] as int?,
      tags: List<String>.from(json['tags'] as List? ?? []),
      description: json['description'] as String?,
      cameraSettings: json['camera_settings'] as Map<String, dynamic>?,
      gpsCoordinates: json['gps_coordinates'] != null
          ? Map<String, double>.from(json['gps_coordinates'] as Map)
          : null,
      status: VideoStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => VideoStatus.active,
      ),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'file_name': fileName,
      'storage_path': storagePath,
      'video_url': videoUrl,
      'file_size_bytes': fileSizeBytes,
      'duration_seconds': durationSeconds,
      'resolution': resolution,
      'fps': fps,
      'codec': codec,
      'bitrate': bitrate,
      'location_type': locationType,
      'location_number': locationNumber,
      'full_location_code': fullLocationCode,
      'username': username,
      'capture_timestamp': captureTimestamp.toIso8601String(),
      'upload_timestamp': uploadTimestamp.toIso8601String(),
      'sort_order': sortOrder,
      'tags': tags,
      'description': description,
      'camera_settings': cameraSettings,
      'gps_coordinates': gpsCoordinates,
      'status': status.name,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VideoModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'VideoModel(id: $id, fileName: $fileName, username: $username)';
  }

  /// الحصول على حجم الملف بصيغة قابلة للقراءة
  String get fileSizeFormatted {
    if (fileSizeBytes == null) return 'غير محدد';
    
    final bytes = fileSizeBytes!;
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  /// الحصول على مدة الفيديو بصيغة قابلة للقراءة
  String get durationFormatted {
    if (durationSeconds == null) return 'غير محدد';
    
    final duration = Duration(seconds: durationSeconds!);
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);
    
    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// التحقق من وجود موقع GPS
  bool get hasGpsCoordinates => gpsCoordinates != null && gpsCoordinates!.isNotEmpty;

  /// الحصول على خط العرض
  double? get latitude => gpsCoordinates?['latitude'];

  /// الحصول على خط الطول
  double? get longitude => gpsCoordinates?['longitude'];

  /// التحقق من كون الفيديو حديث (خلال آخر 24 ساعة)
  bool get isRecent {
    final now = DateTime.now();
    final difference = now.difference(captureTimestamp);
    return difference.inHours <= 24;
  }

  /// التحقق من كون الفيديو عالي الجودة
  bool get isHighQuality {
    if (resolution == null) return false;
    return resolution!.contains('1080') || resolution!.contains('4K') || resolution!.contains('2160');
  }

  /// الحصول على رابط الصورة المصغرة للفيديو
  String? get thumbnailUrl {
    if (videoUrl == null) return null;
    // يمكن إضافة منطق لتوليد رابط الصورة المصغرة للفيديو
    return videoUrl;
  }
}

/// حالات الفيديو
enum VideoStatus {
  active,   // نشط
  deleted,  // محذوف
  archived, // مؤرشف
}

/// فلاتر البحث للفيديوهات
class VideoFilters {
  final String? searchQuery;
  final String? userId;
  final String? username;
  final String? locationType;
  final String? locationNumber;
  final VideoStatus? status;
  final DateTime? captureAfter;
  final DateTime? captureBefore;
  final DateTime? uploadAfter;
  final DateTime? uploadBefore;
  final List<String>? tags;
  final bool? hasGps;
  final int? minFileSize;
  final int? maxFileSize;
  final int? minDuration;
  final int? maxDuration;
  final String? resolution;
  final bool? isHighQuality;
  final int pageSize;
  final int pageOffset;
  final String sortBy;
  final bool sortDescending;

  const VideoFilters({
    this.searchQuery,
    this.userId,
    this.username,
    this.locationType,
    this.locationNumber,
    this.status,
    this.captureAfter,
    this.captureBefore,
    this.uploadAfter,
    this.uploadBefore,
    this.tags,
    this.hasGps,
    this.minFileSize,
    this.maxFileSize,
    this.minDuration,
    this.maxDuration,
    this.resolution,
    this.isHighQuality,
    this.pageSize = 20,
    this.pageOffset = 0,
    this.sortBy = 'capture_timestamp',
    this.sortDescending = true,
  });
}
